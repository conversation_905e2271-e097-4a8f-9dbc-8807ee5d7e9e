<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.Net.Compilers.2.9.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.2.9.0\build\Microsoft.Net.Compilers.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{63F8E899-6DA2-4102-89B5-C3741BCC119A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WealthMart.Web</RootNamespace>
    <AssemblyName>WealthMart.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <TypeScriptToolsVersion>2.1</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.0.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.0.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=*********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.2.0\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.********\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itextsharp.xmlworker.********\lib\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.13.1.12554, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.13.1\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.6.8.0\lib\net461\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.6.8.0\lib\net461\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.6.8.0\lib\net461\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.19.6.0\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess.EntityFramework, Version=6.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.EntityFramework.19.6.0\lib\net45\Oracle.ManagedDataAccess.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.3.0\lib\net46\System.AppContext.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Console, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.3.1\lib\net46\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.7.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=6.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.6.8.0\lib\net461\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.1.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.4.7.0\lib\net46\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.3\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <!-- <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"> -->
    <!-- <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath> -->
    <!-- </Reference> -->
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.3.1\lib\net462\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net462\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.7\lib\net45\System.Web.Http.Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.ReaderWriter, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.1\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="WordToPDF, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WordToPDF.1.0.0\lib\net452\WordToPDF.dll</HintPath>
    </Reference>
    <Reference Include="Z.EntityFramework.Extensions, Version=5.2.8.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.EntityFramework.Extensions.5.2.8\lib\net45\Z.EntityFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="WealthMartTestFramework">
      <HintPath>..\WealthMart.Framework\bin\WealthMartTestFramework.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="**/accountGen.aspx" />
    <Content Include="**/allprospects.aspx" />
    <Content Include="**/apimgt-details.aspx" />
    <Content Include="**/apimgt.aspx" />
    <Content Include="**/AuditTrailLogs.aspx" />
    <Content Include="**/aumPositionAgent.aspx" />
    <Content Include="**/aumReports.aspx" />
    <Content Include="**/changerequest-details.aspx" />
    <Content Include="**/changerequest.aspx" />
    <Content Include="**/user_js\pepListing.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="**/fundPerformances.aspx" />
    <Content Include="**/addBankDetails.aspx" />
    <Content Include="**/user_js\addBankDetails.js" />
    <Content Include="**/email_templates\CustomerProfileUpdate-BankDetail.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="**/email_templates\CustomerProfileUpdate-Biodata.html" />
    <Content Include="**/errorPage.aspx" />
    <Content Include="**/pssAgentsMakerChecker.aspx" />
    <None Include="Connected Services\ADService\service.wsdl" />
    <Content Include="**/clientConsoleNew.aspx" />
    <Content Include="**/ClientMandateView.aspx" />
    <Content Include="**/clientPortfoliosDeluxe.aspx" />
    <Content Include="**/clientTradesListing.aspx" />
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.ADUser.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.BirthdayCelebrants.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.Image.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.PastEmployers.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.ProfessionalQualifications.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.ReturnedMessage.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\ADService\WealthMart.Web.ADService.UpdateUserPictureResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <None Include="Connected Services\AxaBVNApi\paystackverify.disco" />
    <None Include="Connected Services\AxaBVNApi\configuration91.svcinfo" />
    <None Include="Connected Services\AxaBVNApi\configuration.svcinfo" />
    <None Include="Connected Services\AxaBVNApi\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="**/clientbirthdays.aspx" />
    <!-- <Content Include="**/clientConsole.aspx" /> -->
    <Content Include="**/clientProfilesForUpdate.aspx" />
    <Content Include="**/clientpss.aspx" />
    <None Include="Connected Services\ADService\service.disco" />
    <None Include="Connected Services\ADService\configuration91.svcinfo" />
    <None Include="Connected Services\ADService\configuration.svcinfo" />
    <None Include="Connected Services\ADService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="**/consolidatedReports.aspx" />
    <Content Include="**/counterpartieslisting.aspx" />
    <Content Include="**/css\plugins\dual-listbox\dual-listbox.css" />
    <Content Include="**/css\plugins\spinkit\spinkit.css" />
    <Content Include="**/customisedDealsPosition.aspx" />
    <Content Include="**/customisedInvestmentMandates.aspx" />
    <Content Include="**/InvestmentOpportunitiesListing.aspx" />
    <Content Include="**/plugins\toastr\toastr.min.css" />
    <Content Include="**/css\util.css" />
    <Content Include="**/customisedTransactionsLog.aspx" />
    <Content Include="**/dollarStatementGen.aspx" />
    <Content Include="**/dollarStatementNavPrices.aspx" />
    <Content Include="**/dollarStatements.aspx" />
    <Content Include="**/dollarStatementTransactions.aspx" />
    <Content Include="**/dollarStatementTransactionsNew.aspx" />
    <Content Include="**/edit-account-details.aspx" />
    <Content Include="**/editAccount.aspx" />
    <Content Include="**/email_templates\InvestmentMaturityTemplate.html" />
    <Content Include="**/email_templates\PeriodicalClientStatement.html" />
    <Content Include="**/expenseCalculator.aspx" />
    <Content Include="**/giftcards.aspx" />
    <Content Include="**/HolidayManager.aspx" />
    <Content Include="**/inflowlog.aspx" />
    <Content Include="**/InstructionLetter.aspx" />
    <Content Include="**/InvestmentLetterApprovals.aspx" />
    <Content Include="**/InvestmentLetterGen.aspx" />
    <Content Include="**/js\plugins\autocomplete-dropdown\autocomplete.min.js" />
    <Content Include="**/js\plugins\chartJs\2.7.3\chart.min.js" />
    <Content Include="**/js\plugins\dual-listbox\dual-listbox.min.js" />
    <Content Include="**/LiquidationRequests.aspx" />
    <Content Include="**/PepClientsDetails.aspx" />
    <Content Include="**/PepListings.aspx" />
    <Content Include="**/PepRegistration.aspx" />
    <Content Include="**/OldClientBvnValidation.aspx" />
    <Content Include="**/premiumFinanceLog.aspx" />
    <Content Include="**/riskProfilerSelections.aspx" />
    <Content Include="**/templates\InvestmentAdviceContent.html" />
    <Content Include="**/templates\pdf\ClientFDDealAdviceV2.html" />
    <Content Include="**/templates\pdf\InvestmentAdvice-Bonds.html" />
    <Content Include="**/templates\pdf\InvestmentAdvice-TreasuryBill.html" />
    <Content Include="**/templates\pdf\newWelcomeLetter.html" />
    <Content Include="**/untaggedClients.aspx" />
    <Content Include="**/untaggedClientsForApproval.aspx" />
    <Content Include="**/untaggedPssClients.aspx" />
    <Content Include="**/untaggedPssClientsApproval.aspx" />
    <Content Include="**/userdetails.aspx" />
    <Content Include="**/user_js\aumposition.js" />
    <Content Include="**/user_js\aumReports.js" />
    <Content Include="**/user_js\auth.js" />
    <Content Include="**/user_js\clientMandate.js" />
    <Content Include="**/user_js\clientTrades.js" />
    <Content Include="**/user_js\counterpartieslisting.js" />
    <Content Include="**/user_js\customisedTransactionsLog.js" />
    <Content Include="**/user_js\deluxe\activePortfolios.js" />
    <Content Include="**/user_js\investment-opportunities-listing.js" />
    <Content Include="**/user_js\investmentLetter.js" />
    <Content Include="**/user_js\holidaymanager.js" />
    <Content Include="**/user_js\liquidationrequests.js" />
    <Content Include="**/user_js\notificationsService.js" />
    <Content Include="**/js\plugins\xlsx\xlsx.full.min.js" />
    <Content Include="**/newClientJoint.aspx" />
    <Content Include="**/newComplaintLog.aspx" />
    <Content Include="**/prospectDetailsApprovals.aspx" />
    <Content Include="**/RMBTransactionLogs.aspx" />
    <Content Include="**/plugins\autotable.js" />
    <Content Include="**/plugins\canvas.min.js" />
    <Content Include="**/plugins\jqueryoverlay.min.js" />
    <Content Include="**/plugins\jspdf.min.js" />
    <Content Include="**/plugins\jspdf.plugin.autotable.min.js" />
    <Content Include="**/pssCommission.aspx" />
    <Content Include="**/email_templates\ScheduledLiquidationNotification.html" />
    <Content Include="**/email_templates\ScheduledLiquidationFailureNotification.html" />
    <Content Include="**/email_templates\TransferFailureNotification.html" />
    <Content Include="**/feesncommission.aspx" />
    <Content Include="**/giftcards.aspx" />
    <Content Include="**/newClientJoint.aspx" />
    <Content Include="**/PrescheduledLiquidations.aspx" />
    <Content Include="**/offlinePrescheduledRequest.aspx" />
    <Content Include="**/pssusers.aspx" />
    <Content Include="**/clientMandateApprovals.aspx" />
    <Content Include="**/clientMandateEdit.aspx" />
    <Content Include="**/img\._btn_left.gif" />
    <Content Include="**/img\._btn_right.gif" />
    <Content Include="**/img\btn_left.gif" />
    <Content Include="**/img\btn_right.gif" />
    <Content Include="**/img\checkbox.gif" />
    <Content Include="**/img\input\._input_left-focus.gif" />
    <Content Include="**/img\input\._input_left-hover.gif" />
    <Content Include="**/img\input\._input_left.gif" />
    <Content Include="**/img\input\._input_right-focus.gif" />
    <Content Include="**/img\input\._input_right-hover.gif" />
    <Content Include="**/img\input\._input_right.gif" />
    <Content Include="**/img\input\._input_text_left.gif" />
    <Content Include="**/img\input\._input_text_right.gif" />
    <Content Include="**/img\input\input_left-focus.gif" />
    <Content Include="**/img\input\input_left-hover.gif" />
    <Content Include="**/img\input\input_left.gif" />
    <Content Include="**/img\input\input_right-focus.gif" />
    <Content Include="**/img\input\input_right-hover.gif" />
    <Content Include="**/img\input\input_right.gif" />
    <Content Include="**/img\input\input_text_left.gif" />
    <Content Include="**/img\input\input_text_right.gif" />
    <Content Include="**/img\radio.gif" />
    <Content Include="**/img\select_left.gif" />
    <Content Include="**/img\select_right.gif" />
    <Content Include="**/img\textarea\._textarea-mm-focus.gif" />
    <Content Include="**/img\textarea\._textarea-mm-hover.gif" />
    <Content Include="**/img\textarea\._textarea-mm.gif" />
    <Content Include="**/img\textarea\._textarea_bl.gif" />
    <Content Include="**/img\textarea\._textarea_bm.gif" />
    <Content Include="**/img\textarea\._textarea_br.gif" />
    <Content Include="**/img\textarea\._textarea_ml.gif" />
    <Content Include="**/img\textarea\._textarea_mr.gif" />
    <Content Include="**/img\textarea\._textarea_tl.gif" />
    <Content Include="**/img\textarea\._textarea_tm.gif" />
    <Content Include="**/img\textarea\._textarea_tr.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bl-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bl-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bl.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-bm.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-br-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-br-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-br.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-ml-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-ml-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-ml.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mm.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mr-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mr-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-mr.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tl-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tl-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tl.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tm.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tr-focus.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tr-hover.gif" />
    <Content Include="**/img\textarea\notneeded\._textarea-tr.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bl-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bl-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bl.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-bm.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-br-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-br-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-br.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-ml-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-ml-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-ml.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mm.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mr-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mr-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-mr.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tl-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tl-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tl.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tm-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tm-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tm.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tr-focus.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tr-hover.gif" />
    <Content Include="**/img\textarea\notneeded\textarea-tr.gif" />
    <Content Include="**/img\textarea\textarea-mm-focus.gif" />
    <Content Include="**/img\textarea\textarea-mm-hover.gif" />
    <Content Include="**/img\textarea\textarea-mm.gif" />
    <Content Include="**/img\textarea\textarea_bl.gif" />
    <Content Include="**/img\textarea\textarea_bm.gif" />
    <Content Include="**/img\textarea\textarea_br.gif" />
    <Content Include="**/img\textarea\textarea_ml.gif" />
    <Content Include="**/img\textarea\textarea_mr.gif" />
    <Content Include="**/img\textarea\textarea_tl.gif" />
    <Content Include="**/img\textarea\textarea_tm.gif" />
    <Content Include="**/img\textarea\textarea_tr.gif" />
    <Content Include="**/css\jqtransform.css" />
    <Content Include="**/js\jquery-3.1.1.min.js" />
    <Content Include="**/js\jquery.jqtransform.js" />
    <Content Include="**/js\popper.min.js" />
    <Content Include="**/products.aspx" />
    <Content Include="**/purchases.aspx" />
    <Content Include="**/RecurringDebits.aspx" />
    <Content Include="**/referralPoints.aspx" />
    <Content Include="**/RiskProfiler.aspx" />
    <Content Include="**/adminusersApprovals.aspx" />
    <Content Include="**/approvals.aspx" />
    <Content Include="**/aumCredencePostHistory.aspx" />
    <Content Include="**/aumEntries.aspx" />
    <Content Include="**/aumInterestProceeds.aspx" />
    <Content Include="**/aumTransactionsApprovals.aspx" />
    <Content Include="**/css\plugins\dataTables\buttons.dataTables.css" />
    <Content Include="**/css\plugins\dataTables\rowGroup.dataTables.min.css" />
    <Content Include="**/email_templates\LeadEngagementDay14.html" />
    <Content Include="**/email_templates\LeadEngagementDay5.html" />
    <Content Include="**/email_templates\LeadEngagementDay0.html" />
    <Content Include="**/email_templates\RenewalNotice.html" />
    <Content Include="**/email_templates\FixedDepositOrderPlacement.html" />
    <Content Include="**/hniCustomers.aspx" />
    <Content Include="**/aumInvestmentPeriods.aspx" />
    <Content Include="**/IssuerConfiguration.aspx" />
    <Content Include="**/js\angular.min.js" />
    <Content Include="**/manageCounterParty.aspx" />
    <Content Include="**/fdRates.aspx" />
    <Content Include="**/authorizeFDOrders.aspx" />
    <Content Include="**/ClientTagging.aspx" />
    <Content Include="**/portalUpdateRequests.aspx" />
    <Content Include="**/portalupdatedetails.aspx" />
    <None Include="Connected Services\AxaBVNApi\paystackverify.wsdl" />
    <Content Include="**/Connected Services\AxaBVNApi\WealthMart.Web.AxaBVNApi.BVN_MatchResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\AxaBVNApi\WealthMart.Web.AxaBVNApi.FetchBVNDataResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\AxaBVNApi\WealthMart.Web.AxaBVNApi.ResolveAccountNumberResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\AxaBVNApi\WealthMart.Web.AxaBVNApi.VerifyResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <None Include="Connected Services\CredenceAPI\iwebzWebServices.wsdl" />
    <Content Include="**/Connected Services\CredenceAPI\WealthMart.Web.CredenceAPI.clientResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceAPI\WealthMart.Web.CredenceAPI.mastersResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceAPI\WealthMart.Web.CredenceAPI.reportsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceAPI\WealthMart.Web.CredenceAPI.settingsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceAPI\WealthMart.Web.CredenceAPI.transactionResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <None Include="Connected Services\CredenceWS\configuration91.svcinfo" />
    <None Include="Connected Services\CredenceWS\configuration.svcinfo" />
    <None Include="Connected Services\CredenceWS\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Connected Services\CredenceAPI\configuration91.svcinfo" />
    <None Include="Connected Services\CredenceAPI\configuration.svcinfo" />
    <None Include="Connected Services\CredenceAPI\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Connected Services\EkomiWebService\GenerateRatingTags.wsdl" />
    <Content Include="**/Connected Services\EkomiWebService\WealthMart.Web.EkomiWebService.Result.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <None Include="Connected Services\EkomiWebService\GenerateRatingTags.disco" />
    <None Include="Connected Services\EkomiWebService\configuration91.svcinfo" />
    <None Include="Connected Services\EkomiWebService\configuration.svcinfo" />
    <None Include="Connected Services\EkomiWebService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="**/adminusers.aspx" />
    <None Include="Connected Services\KycService\uploadkyc.disco" />
    <None Include="Connected Services\KycService\configuration91.svcinfo" />
    <None Include="Connected Services\KycService\configuration.svcinfo" />
    <None Include="Connected Services\KycService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="**/email_templates\BalanceExhausted.html" />
    <Content Include="**/email_templates\TransferNotification.html" />
    <Content Include="**/email_templates\WelcomeLetter.html" />
    <Content Include="**/js\plugins\dataTables\buttons.colVis.min.js" />
    <Content Include="**/js\plugins\dataTables\buttons.html5.js" />
    <Content Include="**/js\plugins\dataTables\dataTables.buttons.js" />
    <Content Include="**/js\plugins\dataTables\dataTables.rowGroup.min.js" />
    <Content Include="**/JumiaSummary.aspx" />
    <Content Include="**/JumiaCommissions.aspx" />
    <Content Include="**/manageFdOrders.aspx" />
    <Content Include="**/manageBills.aspx" />
    <Content Include="**/manageFdSettlement.aspx" />
    <Content Include="**/mine\img\axamansardlogo.png" />
    <None Include="**/Connected Services\KycService\uploadkyc.wsdl" />
    <Content Include="**/Connected Services\KycService\WealthMart.Web.KycService.HelloWorldResponse1.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\KycService\WealthMart.Web.KycService.UploadKYCDocumentResponse1.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/NewRequests.aspx" />
    <Content Include="**/PaymentLogs.aspx" />
    <Content Include="**/plugins\html5shiv.min.js" />
    <Content Include="**/plugins\iCheck\icheck.min.js" />
    <Content Include="**/plugins\iCheck\square\aero%402x.png" />
    <Content Include="**/plugins\iCheck\square\aero.css" />
    <Content Include="**/plugins\iCheck\square\aero.png" />
    <Content Include="**/plugins\iCheck\square\blue%402x.png" />
    <Content Include="**/plugins\iCheck\square\blue.css" />
    <Content Include="**/plugins\iCheck\square\blue.png" />
    <Content Include="**/plugins\iCheck\square\green%402x.png" />
    <Content Include="**/plugins\iCheck\square\green.css" />
    <Content Include="**/plugins\iCheck\square\green.png" />
    <Content Include="**/plugins\iCheck\square\grey%402x.png" />
    <Content Include="**/plugins\iCheck\square\grey.css" />
    <Content Include="**/plugins\iCheck\square\grey.png" />
    <Content Include="**/plugins\iCheck\square\orange%402x.png" />
    <Content Include="**/plugins\iCheck\square\orange.css" />
    <Content Include="**/plugins\iCheck\square\orange.png" />
    <Content Include="**/plugins\iCheck\square\pink%402x.png" />
    <Content Include="**/plugins\iCheck\square\pink.css" />
    <Content Include="**/plugins\iCheck\square\pink.png" />
    <Content Include="**/plugins\iCheck\square\purple%402x.png" />
    <Content Include="**/plugins\iCheck\square\purple.css" />
    <Content Include="**/plugins\iCheck\square\purple.png" />
    <Content Include="**/plugins\iCheck\square\red%402x.png" />
    <Content Include="**/plugins\iCheck\square\red.css" />
    <Content Include="**/plugins\iCheck\square\red.png" />
    <Content Include="**/plugins\iCheck\square\square%402x.png" />
    <Content Include="**/plugins\iCheck\square\square.css" />
    <Content Include="**/plugins\iCheck\square\square.png" />
    <Content Include="**/plugins\iCheck\square\yellow%402x.png" />
    <Content Include="**/plugins\iCheck\square\yellow.css" />
    <Content Include="**/plugins\iCheck\square\yellow.png" />
    <Content Include="**/plugins\iCheck\square\_all.css" />
    <Content Include="**/plugins\jquery-1.11.3.min.js" />
    <Content Include="**/plugins\respond.min.js" />
    <Content Include="**/plugins\showloading\jquery.showLoading.js" />
    <Content Include="**/plugins\showloading\jquery.showLoading.min.js" />
    <Content Include="**/plugins\showloading\showLoading.css" />
    <Content Include="**/plugins\sweetAlert\sweetalert-dev.js" />
    <Content Include="**/plugins\sweetAlert\sweetalert.css" />
    <Content Include="**/plugins\sweetAlert\sweetalert.min.js" />
    <Content Include="**/plugins\sweetAlert\sweetalert2.js" />
    <Content Include="**/plugins\table2excel.js" />
    <Content Include="**/pssinvestomania.aspx" />
    <Content Include="**/investomania.aspx" />
    <Content Include="**/RoboAdvisor.aspx" />
    <Content Include="**/Scripts\datejs\date.js" />
    <Content Include="**/Scripts\esm\popper-utils.js" />
    <Content Include="**/Scripts\esm\popper-utils.min.js" />
    <Content Include="**/Scripts\esm\popper.js" />
    <Content Include="**/Scripts\esm\popper.min.js" />
    <Content Include="**/Scripts\moment-with-locales.js" />
    <Content Include="**/Scripts\moment-with-locales.min.js" />
    <Content Include="**/Scripts\moment.js" />
    <Content Include="**/Scripts\moment.min.js" />
    <Content Include="**/Scripts\popper-utils.js" />
    <Content Include="**/Scripts\popper-utils.min.js" />
    <Content Include="**/Scripts\popper.js" />
    <Content Include="**/Scripts\popper.min.js" />
    <Content Include="**/Scripts\src\index.js" />
    <Content Include="**/Scripts\src\methods\defaults.js" />
    <Content Include="**/Scripts\src\methods\destroy.js" />
    <Content Include="**/Scripts\src\methods\disableEventListeners.js" />
    <Content Include="**/Scripts\src\methods\enableEventListeners.js" />
    <Content Include="**/Scripts\src\methods\placements.js" />
    <Content Include="**/Scripts\src\methods\update.js" />
    <Content Include="**/Scripts\src\modifiers\applyStyle.js" />
    <Content Include="**/Scripts\src\modifiers\arrow.js" />
    <Content Include="**/Scripts\src\modifiers\computeStyle.js" />
    <Content Include="**/Scripts\src\modifiers\flip.js" />
    <Content Include="**/Scripts\src\modifiers\hide.js" />
    <Content Include="**/Scripts\src\modifiers\index.js" />
    <Content Include="**/Scripts\src\modifiers\inner.js" />
    <Content Include="**/Scripts\src\modifiers\keepTogether.js" />
    <Content Include="**/Scripts\src\modifiers\offset.js" />
    <Content Include="**/Scripts\src\modifiers\preventOverflow.js" />
    <Content Include="**/Scripts\src\modifiers\shift.js" />
    <Content Include="**/Scripts\src\utils\clockwise.js" />
    <Content Include="**/Scripts\src\utils\computeAutoPlacement.js" />
    <Content Include="**/Scripts\src\utils\debounce.js" />
    <Content Include="**/Scripts\src\utils\find.js" />
    <Content Include="**/Scripts\src\utils\findCommonOffsetParent.js" />
    <Content Include="**/Scripts\src\utils\findIndex.js" />
    <Content Include="**/Scripts\src\utils\getBordersSize.js" />
    <Content Include="**/Scripts\src\utils\getBoundaries.js" />
    <Content Include="**/Scripts\src\utils\getBoundingClientRect.js" />
    <Content Include="**/Scripts\src\utils\getClientRect.js" />
    <Content Include="**/Scripts\src\utils\getFixedPositionOffsetParent.js" />
    <Content Include="**/Scripts\src\utils\getOffsetParent.js" />
    <Content Include="**/Scripts\src\utils\getOffsetRect.js" />
    <Content Include="**/Scripts\src\utils\getOffsetRectRelativeToArbitraryNode.js" />
    <Content Include="**/Scripts\src\utils\getOppositePlacement.js" />
    <Content Include="**/Scripts\src\utils\getOppositeVariation.js" />
    <Content Include="**/Scripts\src\utils\getOuterSizes.js" />
    <Content Include="**/Scripts\src\utils\getParentNode.js" />
    <Content Include="**/Scripts\src\utils\getPopperOffsets.js" />
    <Content Include="**/Scripts\src\utils\getReferenceNode.js" />
    <Content Include="**/Scripts\src\utils\getReferenceOffsets.js" />
    <Content Include="**/Scripts\src\utils\getRoot.js" />
    <Content Include="**/Scripts\src\utils\getRoundedOffsets.js" />
    <Content Include="**/Scripts\src\utils\getScroll.js" />
    <Content Include="**/Scripts\src\utils\getScrollParent.js" />
    <Content Include="**/Scripts\src\utils\getStyleComputedProperty.js" />
    <Content Include="**/Scripts\src\utils\getSupportedPropertyName.js" />
    <Content Include="**/Scripts\src\utils\getViewportOffsetRectRelativeToArtbitraryNode.js" />
    <Content Include="**/Scripts\src\utils\getWindow.js" />
    <Content Include="**/Scripts\src\utils\getWindowSizes.js" />
    <Content Include="**/Scripts\src\utils\includeScroll.js" />
    <Content Include="**/Scripts\src\utils\index.js" />
    <Content Include="**/Scripts\src\utils\isBrowser.js" />
    <Content Include="**/Scripts\src\utils\isFixed.js" />
    <Content Include="**/Scripts\src\utils\isFunction.js" />
    <Content Include="**/Scripts\src\utils\isIE.js" />
    <Content Include="**/Scripts\src\utils\isModifierEnabled.js" />
    <Content Include="**/Scripts\src\utils\isModifierRequired.js" />
    <Content Include="**/Scripts\src\utils\isNumeric.js" />
    <Content Include="**/Scripts\src\utils\isOffsetContainer.js" />
    <Content Include="**/Scripts\src\utils\removeEventListeners.js" />
    <Content Include="**/Scripts\src\utils\runModifiers.js" />
    <Content Include="**/Scripts\src\utils\setAttributes.js" />
    <Content Include="**/Scripts\src\utils\setStyles.js" />
    <Content Include="**/Scripts\src\utils\setupEventListeners.js" />
    <Content Include="**/Scripts\umd\popper-utils.js" />
    <Content Include="**/Scripts\umd\popper-utils.min.js" />
    <Content Include="**/Scripts\umd\popper.js" />
    <Content Include="**/Scripts\umd\popper.min.js" />
    <Content Include="**/securities.aspx" />
    <Content Include="**/serviceLogs.aspx" />
    <Content Include="**/smartConsole.aspx" />
    <Content Include="**/Connected Services\KycService\WealthMart.Web.KycService.InvResult.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/plugins\showloading.zip" />
    <None Include="Properties\PublishProfiles\FolderProfile1.pubxml" />
    <None Include="Properties\PublishProfiles\WealthMart.Web Publish.pubxml" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="**/Scripts\modernizr-2.8.3.js" />
    <Content Include="**/Scripts\respond.js" />
    <Content Include="**/Scripts\respond.matchmedia.addListener.js" />
    <Content Include="**/Scripts\respond.matchmedia.addListener.min.js" />
    <Content Include="**/Scripts\respond.min.js" />
    <Content Include="**/kycVerificationStats.aspx" />
    <Content Include="**/aumposition.aspx" />
    <Content Include="**/smartStatements.aspx" />
    <Content Include="**/smartRecon.aspx" />
    <Content Include="**/stpentries.aspx" />
    <Content Include="**/STRequests.aspx" />
    <Content Include="**/templates\pdf\ClientFDDealAdvice.html" />
    <Content Include="**/templates\pdf\DollarPortfolioStatement.html" />
    <Content Include="**/templates\pdf\EmbassyLetter.html" />
    <Content Include="**/email_templates\giftcardreceipient.html" />
    <Content Include="**/email_templates\giftcardsender.html" />
    <Content Include="**/manageBonds.aspx" />
    <Content Include="**/authorizeBonds.aspx" />
    <Content Include="**/authorizeNewClients.aspx" />
    <Content Include="**/authorizeFDdeals.aspx" />
    <Content Include="**/authorizeEquities.aspx" />
    <Content Include="**/css\animate.css" />
    <Content Include="**/css\bootstrap-datepicker3.min.css" />
    <Content Include="**/css\bootstrap-datetimepicker.min.css" />
    <Content Include="**/css\bootstrap1.min.css" />
    <Content Include="**/css\bootstrap.min.css" />
    <Content Include="**/css\chosen.min.css" />
    <Content Include="**/css\ionicons.min.css" />
    <Content Include="**/css\maince.css" />
    <Content Include="**/css\patterns\3.png" />
    <Content Include="**/css\patterns\4.png" />
    <Content Include="**/css\patterns\header-profile-skin-1.png" />
    <Content Include="**/css\patterns\header-profile-skin-2.png" />
    <Content Include="**/css\patterns\header-profile-skin-3.png" />
    <Content Include="**/css\patterns\header-profile.png" />
    <Content Include="**/css\patterns\shattered.png" />
    <Content Include="**/css\plugins\awesome-bootstrap-checkbox\awesome-bootstrap-checkbox.css" />
    <Content Include="**/css\plugins\blueimp\css\blueimp-gallery.min.css" />
    <Content Include="**/css\plugins\blueimp\img\error.png" />
    <Content Include="**/css\plugins\blueimp\img\error.svg" />
    <Content Include="**/css\plugins\blueimp\img\loading.gif" />
    <Content Include="**/css\plugins\blueimp\img\play-pause.png" />
    <Content Include="**/css\plugins\blueimp\img\play-pause.svg" />
    <Content Include="**/css\plugins\blueimp\img\video-play.png" />
    <Content Include="**/css\plugins\blueimp\img\video-play.svg" />
    <Content Include="**/css\plugins\bootstrap-markdown\bootstrap-markdown.min.css" />
    <Content Include="**/css\plugins\bootstrap-tagsinput\bootstrap-tagsinput.css" />
    <Content Include="**/css\plugins\bootstrapSocial\bootstrap-social.css" />
    <Content Include="**/css\plugins\bootstrapTour\bootstrap-tour.min.css" />
    <Content Include="**/css\plugins\c3\c3.min.css" />
    <Content Include="**/css\plugins\chartist\chartist.min.css" />
    <Content Include="**/css\plugins\chosen\bootstrap-chosen.css" />
    <Content Include="**/css\plugins\chosen\chosen-sprite%402x.png" />
    <Content Include="**/css\plugins\chosen\chosen-sprite.png" />
    <Content Include="**/css\plugins\chosen\chosen.css" />
    <Content Include="**/css\plugins\clockpicker\clockpicker.css" />
    <Content Include="**/css\plugins\codemirror\ambiance.css" />
    <Content Include="**/css\plugins\codemirror\codemirror.css" />
    <Content Include="**/css\plugins\colorpicker\bootstrap-colorpicker.min.css" />
    <Content Include="**/css\plugins\cropper\cropper.min.css" />
    <Content Include="**/css\plugins\datapicker\datepicker3.css" />
    <Content Include="**/css\plugins\dataTables\datatables.min.css" />
    <Content Include="**/css\plugins\daterangepicker\daterangepicker-bs3.css" />
    <Content Include="**/css\plugins\dropzone\basic.css" />
    <Content Include="**/css\plugins\dropzone\dropzone.css" />
    <Content Include="**/css\plugins\dualListbox\bootstrap-duallistbox.min.css" />
    <Content Include="**/css\plugins\footable\fonts\footable.svg" />
    <Content Include="**/css\plugins\footable\footable.core.css" />
    <Content Include="**/css\plugins\fullcalendar\fullcalendar.css" />
    <Content Include="**/css\plugins\fullcalendar\fullcalendar.print.css" />
    <Content Include="**/css\plugins\iCheck\custom.css" />
    <Content Include="**/css\plugins\iCheck\green%402x.png" />
    <Content Include="**/css\plugins\iCheck\green.png" />
    <Content Include="**/css\plugins\images\bootstrap-colorpicker\alpha-horizontal.png" />
    <Content Include="**/css\plugins\images\bootstrap-colorpicker\alpha.png" />
    <Content Include="**/css\plugins\images\bootstrap-colorpicker\hue-horizontal.png" />
    <Content Include="**/css\plugins\images\bootstrap-colorpicker\hue.png" />
    <Content Include="**/css\plugins\images\bootstrap-colorpicker\saturation.png" />
    <Content Include="**/css\plugins\images\sprite-skin-flat.png" />
    <Content Include="**/css\plugins\ionRangeSlider\ion.rangeSlider.css" />
    <Content Include="**/css\plugins\ionRangeSlider\ion.rangeSlider.skinFlat.css" />
    <Content Include="**/css\plugins\jasny\jasny-bootstrap.min.css" />
    <Content Include="**/css\plugins\jqGrid\ui.jqgrid.css" />
    <Content Include="**/css\plugins\jQueryUI\images\animated-overlay.gif" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-icons_222222_256x240.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-icons_454545_256x240.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-icons_888888_256x240.png" />
    <Content Include="**/css\plugins\jQueryUI\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="**/css\plugins\jQueryUI\jquery-ui-1.10.4.custom.min.css" />
    <Content Include="**/css\plugins\jQueryUI\jquery-ui.css" />
    <Content Include="**/css\plugins\jsTree\style.min.css" />
    <Content Include="**/css\plugins\ladda\ladda-themeless.min.css" />
    <Content Include="**/css\plugins\morris\morris-0.4.3.min.css" />
    <Content Include="**/css\plugins\nouslider\jquery.nouislider.css" />
    <Content Include="**/css\plugins\select2\select2.min.css" />
    <Content Include="**/css\plugins\slick\ajax-loader.gif" />
    <Content Include="**/css\plugins\slick\fonts\slick.svg" />
    <Content Include="**/css\plugins\slick\slick-theme.css" />
    <Content Include="**/css\plugins\slick\slick.css" />
    <Content Include="**/css\plugins\steps\jquery.steps.css" />
    <Content Include="**/css\plugins\summernote\summernote-bs3.css" />
    <Content Include="**/css\plugins\summernote\summernote-bs4.css" />
    <Content Include="**/css\plugins\summernote\summernote.css" />
    <Content Include="**/css\plugins\sweetalert\sweetalert.css" />
    <Content Include="**/css\plugins\switchery\switchery.css" />
    <Content Include="**/css\plugins\textSpinners\spinners.css" />
    <Content Include="**/css\plugins\toastr\toastr.min.css" />
    <Content Include="**/css\plugins\touchspin\jquery.bootstrap-touchspin.min.css" />
    <Content Include="**/css\style.css" />
    <Content Include="**/DashBoard.aspx" />
    <Content Include="**/Default.aspx" />
    <Content Include="**/email_templates\axamansardlogo.jpg" />
    <Content Include="**/email_templates\KycNotProvided.html" />
    <Content Include="**/email_templates\KycDeclined.html" />
    <Content Include="**/email_templates\ClientCPDealMaturity.html" />
    <Content Include="**/favicon.ico" />
    <Content Include="**/font-awesome\css\font-awesome.css" />
    <Content Include="**/font-awesome\css\font-awesome.min.css" />
    <Content Include="**/font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="**/fonts\glyphicons-halflings-regular.svg" />
    <Content Include="**/**/Global.asax" />
    <Content Include="**/img\axamansardlogo.jpg" />
    <Content Include="**/img\loading.gif" />
    <Content Include="**/img\profile_small.jpg" />
    <Content Include="**/js\bootstrap2.js" />
    <Content Include="**/js\bootstrap2.min.js" />
    <Content Include="**/js\bootstrap.js" />
    <Content Include="**/js\bootstrap.min.js" />
    <Content Include="**/js\demo\chartjs-demo.js" />
    <Content Include="**/js\demo\flot-demo.js" />
    <Content Include="**/js\demo\morris-demo.js" />
    <Content Include="**/js\demo\peity-demo.js" />
    <Content Include="**/js\demo\rickshaw-demo.js" />
    <Content Include="**/js\demo\sparkline-demo.js" />
    <Content Include="**/js\inspinia.js" />
    <Content Include="**/js\jquery-2.1.1.js" />
    <Content Include="**/js\jquery-ui-1.10.4.min.js" />
    <Content Include="**/js\jquery-ui.custom.min.js" />
    <Content Include="**/js\plugins\blueimp\jquery.blueimp-gallery.min.js" />
    <Content Include="**/js\plugins\bootstrap-markdown\bootstrap-markdown.js" />
    <Content Include="**/js\plugins\bootstrap-markdown\markdown.js" />
    <Content Include="**/js\plugins\bootstrapTour\bootstrap-tour.min.js" />
    <Content Include="**/js\plugins\c3\c3.min.js" />
    <Content Include="**/js\plugins\chartist\chartist.min.js" />
    <Content Include="**/js\plugins\chartJs\Chart.min.js" />
    <Content Include="**/js\plugins\chosen\chosen.jquery.js" />
    <Content Include="**/js\plugins\clipboard\clipboard.min.js" />
    <Content Include="**/js\plugins\clockpicker\clockpicker.js" />
    <Content Include="**/js\plugins\codemirror\codemirror.js" />
    <Content Include="**/js\plugins\codemirror\mode\javascript\javascript.js" />
    <Content Include="**/js\plugins\colorpicker\bootstrap-colorpicker.min.js" />
    <Content Include="**/js\plugins\cropper\cropper.min.js" />
    <Content Include="**/js\plugins\d3\d3.min.js" />
    <Content Include="**/js\plugins\datapicker\bootstrap-datepicker.js" />
    <Content Include="**/js\plugins\dataTables\datatables.min.js" />
    <Content Include="**/js\plugins\daterangepicker\daterangepicker.js" />
    <Content Include="**/js\plugins\diff_match_patch\javascript\diff_match_patch.js" />
    <Content Include="**/js\plugins\dotdotdot\jquery.dotdotdot.min.js" />
    <Content Include="**/js\plugins\dropzone\dropzone.js" />
    <Content Include="**/js\plugins\easypiechart\jquery.easypiechart.js" />
    <Content Include="**/js\plugins\flot\curvedLines.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.pie.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.resize.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.spline.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.symbol.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.time.js" />
    <Content Include="**/js\plugins\flot\jquery.flot.tooltip.min.js" />
    <Content Include="**/js\plugins\footable\footable.all.min.js" />
    <Content Include="**/js\plugins\fullcalendar\fullcalendar.min.js" />
    <Content Include="**/js\plugins\fullcalendar\moment.min.js" />
    <Content Include="**/js\plugins\gritter\jquery.gritter.css" />
    <Content Include="**/js\plugins\gritter\jquery.gritter.min.js" />
    <Content Include="**/js\plugins\i18next\i18next.min.js" />
    <Content Include="**/js\plugins\iCheck\icheck.min.js" />
    <Content Include="**/js\plugins\idle-timer\idle-timer.min.js" />
    <Content Include="**/js\plugins\ionRangeSlider\ion.rangeSlider.min.js" />
    <Content Include="**/js\plugins\jasny\jasny-bootstrap.min.js" />
    <Content Include="**/js\plugins\jeditable\jquery.jeditable.js" />
    <Content Include="**/js\plugins\jqGrid\i18n\grid.locale-en.js" />
    <Content Include="**/js\plugins\jqGrid\jquery.jqGrid.min.js" />
    <Content Include="**/js\plugins\jquery-ui\jquery-ui.min.js" />
    <Content Include="**/js\plugins\jsKnob\jquery.knob.js" />
    <Content Include="**/js\plugins\jsTree\jstree.min.js" />
    <Content Include="**/js\plugins\jvectormap\jquery-jvectormap-2.0.2.min.js" />
    <Content Include="**/js\plugins\jvectormap\jquery-jvectormap-world-mill-en.js" />
    <Content Include="**/js\plugins\ladda\ladda.jquery.min.js" />
    <Content Include="**/js\plugins\ladda\ladda.min.js" />
    <Content Include="**/js\plugins\ladda\spin.min.js" />
    <Content Include="**/js\plugins\masonary\masonry.pkgd.min.js" />
    <Content Include="**/js\plugins\metisMenu\jquery.metisMenu.js" />
    <Content Include="**/js\plugins\morris\morris.js" />
    <Content Include="**/js\plugins\morris\raphael-2.1.0.min.js" />
    <Content Include="**/js\plugins\nestable\jquery.nestable.js" />
    <Content Include="**/js\plugins\nouslider\jquery.nouislider.min.js" />
    <Content Include="**/js\plugins\pace\pace.min.js" />
    <Content Include="**/js\plugins\peity\jquery.peity.min.js" />
    <Content Include="**/js\plugins\preetyTextDiff\jquery.pretty-text-diff.min.js" />
    <Content Include="**/js\plugins\rickshaw\rickshaw.min.js" />
    <Content Include="**/js\plugins\rickshaw\vendor\d3.v3.js" />
    <Content Include="**/js\plugins\select2\select2.full.min.js" />
    <Content Include="**/js\plugins\slick\slick.min.js" />
    <Content Include="**/js\plugins\slimscroll\jquery.slimscroll.min.js" />
    <Content Include="**/js\plugins\sparkline\jquery.sparkline.min.js" />
    <Content Include="**/js\plugins\steps\jquery.steps.min.js" />
    <Content Include="**/js\plugins\summernote\summernote.min.js" />
    <Content Include="**/js\plugins\sweetalert\sweetalert.min.js" />
    <Content Include="**/js\plugins\switchery\switchery.js" />
    <Content Include="**/js\plugins\tinycon\tinycon.min.js" />
    <Content Include="**/js\plugins\toastr\toastr.min.js" />
    <Content Include="**/js\plugins\touchspin\jquery.bootstrap-touchspin.min.js" />
    <Content Include="**/js\plugins\validate\jquery.validate.min.js" />
    <Content Include="**/js\plugins\video\responsible-video.js" />
    <Content Include="**/js\plugins\wow\wow.min.js" />
    <Content Include="**/Login.aspx" />
    <Content Include="**/mine\css\custom.css" />
    <Content Include="**/mine\css\fullcalendar.min.css" />
    <Content Include="**/mine\css\fullcalendar.print.min.css" />
    <Content Include="**/mine\css\hint.css" />
    <Content Include="**/mine\css\overrides.css" />
    <Content Include="**/mine\css\tooltips.css" />
    <Content Include="**/mine\img\axamansardlogo.jpg" />
    <Content Include="**/mine\img\axamansardlogoV3.png" />
    <Content Include="**/mine\img\axamansardlogo_.jpg" />
    <Content Include="**/mine\img\logo.png" />
    <Content Include="**/mine\img\rolling_gif.svg" />
    <Content Include="**/mine\img\rolling_gif2.gif" />
    <Content Include="**/mine\js\amcharts\amcharts.js" />
    <Content Include="**/mine\js\amcharts\serial.js" />
    <Content Include="**/mine\js\amcharts\themes\light.js" />
    <Content Include="**/mine\js\app.js" />
    <Content Include="**/mine\js\bootstrap-datetimepicker.min.js" />
    <Content Include="**/mine\js\chosen.jquery.min.js" />
    <Content Include="**/mine\js\dashboard_graph.js" />
    <Content Include="**/mine\js\fullcalendar.min.js" />
    <Content Include="**/mine\js\jquery.mask.min.js" />
    <Content Include="**/mine\js\moment.min.js" />
    <Content Include="**/mine\js\tooltip.js" />
    <Content Include="**/mine\js\validator.js" />
    <Content Include="**/templates\Credence_Query_Live.txt" />
    <Content Include="**/embassyletter.aspx" />
    <Content Include="**/templates\pdf\FDPlacementLetter%281%29.html" />
    <Content Include="**/templates\pdf\InvestmentAdvice.html" />
    <Content Include="**/templates\pdf\CarbonFDPlacementLetter.html" />
    <Content Include="**/templates\pdf\FixedDepositOrderInvestmentAdvice.html" />
    <Content Include="**/ticketOrderCreate.aspx" />
    <Content Include="**/ticketOrdersApproval.aspx" />
    <Content Include="**/updateBankDetails.aspx" />
    <Content Include="**/newClient.aspx" />
    <Content Include="**/Site.Master" />
    <Content Include="**/email_templates\ClientCPDealBooking.html" />
    <Content Include="**/templates\Credence_Query_Test.txt" />
    <Content Include="**/**/templates\pdf\MicrocredFDPlacementLetter.html" />
    <Content Include="**/templates\pdf\ClientCPDealAdvice.html" />
    <Content Include="**/templates\pdf\css\InvestmentAdvice.css" />
    <Content Include="**/templates\pdf\img\axamansardlogo.png" />
    <Content Include="**/templates\pdf\img\logo.png" />
    <Content Include="**/authorizeCPdeals.aspx" />
    <Content Include="**/templates\pdf\welcomeletter.html" />
    <Content Include="**/dealListing.aspx" />
    <Content Include="**/smartInflowLog.aspx" />
    <Content Include="**/uploadInflowOutflow.aspx" />
    <Content Include="**/newOfflineRequest.aspx" />
    <Content Include="**/uploadOutflowLog.aspx" />
    <Content Include="**/uploadStatements.aspx" />
    <Content Include="**/user_js\accounts.js" />
    <Content Include="**/user_js\apikeysgen.js" />
    <Content Include="**/user_js\auditlog.js" />
    <Content Include="**/user_js\aumtransactionapprovals.js" />
    <!-- <Content Include="**/user_js\clientconsole.js" /> -->
    <Content Include="**/user_js\clientmemoapprovals.js" />
    <Content Include="**/user_js\clientmemoedit.js" />
    <Content Include="**/user_js\clientupdateapproval.js" />
    <Content Include="**/user_js\complaintsLog.js" />
    <Content Include="**/user_js\consolidatedreports.js" />
    <Content Include="**/user_js\dollarStatement.js" />
    <Content Include="**/user_js\editaccount.js" />
    <Content Include="**/user_js\editaccountdetails.js" />
    <Content Include="**/user_js\expenseCalcualator.js" />
    <Content Include="**/user_js\fundperformance.js" />
    <Content Include="**/user_js\giftcards.js" />
    <Content Include="**/user_js\inflows.js" />
    <Content Include="**/user_js\newClientJoint.js" />
    <Content Include="**/user_js\pepClientDetails.js" />
    <Content Include="**/user_js\pepListings.js" />
    <Content Include="**/user_js\oldclientbvnvalidation.js" />
    <Content Include="**/user_js\premiumFinanceLogs.js" />
    <Content Include="**/user_js\profiler.js" />
    <Content Include="**/user_js\pss.js" />
    <Content Include="**/user_js\offlinePrescheduledrequest.js" />
    <Content Include="**/user_js\pssCommissions.js" />
    <Content Include="**/user_js\pssMakerChecker.js" />
    <Content Include="**/user_js\pssusers.js" />
    <Content Include="**/user_js\newofflinerequest.js" />
    <Content Include="**/user_js\app.js" />
    <Content Include="**/user_js\aumcredenceschedule.js" />
    <Content Include="**/user_js\aumentries.js" />
    <Content Include="**/user_js\auminterestproceeds.js" />
    <Content Include="**/user_js\auminvestmentperiods.js" />
    <Content Include="**/user_js\adminusersapprovals.js" />
    <Content Include="**/user_js\bankapprovals.js" />
    <Content Include="**/user_js\currencyFormatter.js" />
    <Content Include="**/user_js\fdorders.js" />
    <Content Include="**/user_js\dailyrates.js" />
    <Content Include="**/user_js\adminusers.js" />
    <Content Include="**/user_js\clienttagging.js" />
    <Content Include="**/user_js\dealListing.js" />
    <Content Include="**/user_js\investomania.js" />
    <Content Include="**/user_js\issuerconfig.js" />
    <Content Include="**/user_js\manageFDOrders.js" />
    <Content Include="**/user_js\paymentLogs.js" />
    <Content Include="**/user_js\newrequests.js" />
    <Content Include="**/user_js\renewalnotice.js" />
    <Content Include="**/user_js\roboadvisor.js" />
    <Content Include="**/user_js\settings.js" />
    <Content Include="**/user_js\smartConsole.js" />
    <Content Include="**/user_js\smartRecon.js" />
    <Content Include="**/user_js\inflowLog.js" />
    <Content Include="**/user_js\manageBills.js" />
    <Content Include="**/user_js\manageBonds.js" />
    <Content Include="**/user_js\strequests.js" />
    <Content Include="**/user_js\ticketOrders.js" />
    <Content Include="**/user_js\transactionLog.js" />
    <Content Include="**/user_js\updateBankDetails.js" />
    <Content Include="**/user_js\newClient.js" />
    <Content Include="**/user_js\authorizeNewClients.js" />
    <Content Include="**/user_js\userdetails.js" />
    <Content Include="**/user_js\util.js" />
    <Content Include="**/user_js\verifyKYC.js" />
    <Content Include="**/user_js\portalUpdateRequests.js" />
    <Content Include="**/verifyKYC.aspx" />
    <Content Include="**/ViewSwitcher.ascx" />
    <Content Include="**/Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="**/accountGen.aspx.cs">
      <DependentUpon>accountGen.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/accountGen.aspx.designer.cs">
      <DependentUpon>accountGen.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/allprospects.aspx.cs">
      <DependentUpon>allprospects.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/allprospects.aspx.designer.cs">
      <DependentUpon>allprospects.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/apimgt-details.aspx.cs">
      <DependentUpon>apimgt-details.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/apimgt-details.aspx.designer.cs">
      <DependentUpon>apimgt-details.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/apimgt.aspx.cs">
      <DependentUpon>apimgt.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/apimgt.aspx.designer.cs">
      <DependentUpon>apimgt.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/AuditTrailLogs.aspx.cs">
      <DependentUpon>AuditTrailLogs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/AuditTrailLogs.aspx.designer.cs">
      <DependentUpon>AuditTrailLogs.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumPositionAgent.aspx.cs">
      <DependentUpon>aumPositionAgent.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumPositionAgent.aspx.designer.cs">
      <DependentUpon>aumPositionAgent.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumReports.aspx.cs">
      <DependentUpon>aumReports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumReports.aspx.designer.cs">
      <DependentUpon>aumReports.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/changerequest-details.aspx.cs">
      <DependentUpon>changerequest-details.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/changerequest-details.aspx.designer.cs">
      <DependentUpon>changerequest-details.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/changerequest.aspx.cs">
      <DependentUpon>changerequest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/changerequest.aspx.designer.cs">
      <DependentUpon>changerequest.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientbirthdays.aspx.cs">
      <DependentUpon>clientbirthdays.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientbirthdays.aspx.designer.cs">
      <DependentUpon>clientbirthdays.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientConsoleNew.aspx.cs">
      <DependentUpon>clientConsoleNew.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientConsoleNew.aspx.designer.cs">
      <DependentUpon>clientConsoleNew.aspx</DependentUpon>
    </Compile>
    <!-- <Compile Include="**/clientConsole.aspx.cs">
      <DependentUpon>clientConsole.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientConsole.aspx.designer.cs">
      <DependentUpon>clientConsole.aspx</DependentUpon>
    </Compile> -->
    <Compile Include="**/ClientMandateView.aspx.cs">
      <DependentUpon>ClientMandateView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/ClientMandateView.aspx.designer.cs">
      <DependentUpon>ClientMandateView.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientMandateApprovals.aspx.cs">
      <DependentUpon>clientMandateApprovals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientMandateApprovals.aspx.designer.cs">
      <DependentUpon>clientMandateApprovals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientMandateEdit.aspx.cs">
      <DependentUpon>clientMandateEdit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientMandateEdit.aspx.designer.cs">
      <DependentUpon>clientMandateEdit.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientPortfoliosDeluxe.aspx.cs">
      <DependentUpon>clientPortfoliosDeluxe.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientPortfoliosDeluxe.aspx.designer.cs">
      <DependentUpon>clientPortfoliosDeluxe.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientProfilesForUpdate.aspx.cs">
      <DependentUpon>clientProfilesForUpdate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientProfilesForUpdate.aspx.designer.cs">
      <DependentUpon>clientProfilesForUpdate.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/clientpss.aspx.cs">
      <DependentUpon>clientpss.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientpss.aspx.designer.cs">
      <DependentUpon>clientpss.aspx</DependentUpon>
    </Compile>
    <Compile Include="clientTradesListing.aspx.cs">
      <DependentUpon>clientTradesListing.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/clientTradesListing.aspx.designer.cs">
      <DependentUpon>clientTradesListing.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\ADService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\AxaBVNApi\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/consolidatedReports.aspx.cs">
      <DependentUpon>consolidatedReports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/consolidatedReports.aspx.designer.cs">
      <DependentUpon>consolidatedReports.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/counterpartieslisting.aspx.cs">
      <SubType>ASPXCodeBehind</SubType>
      <DependentUpon>counterpartieslisting.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/counterpartieslisting.aspx.designer.cs">
      <DependentUpon>counterpartieslisting.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/customisedDealsPosition.aspx.cs">
      <DependentUpon>customisedDealsPosition.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/customisedDealsPosition.aspx.designer.cs">
      <DependentUpon>customisedDealsPosition.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/customisedInvestmentMandates.aspx.cs">
      <DependentUpon>customisedInvestmentMandates.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/customisedInvestmentMandates.aspx.designer.cs">
      <DependentUpon>customisedInvestmentMandates.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/customisedTransactionsLog.aspx.cs">
      <DependentUpon>customisedTransactionsLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/customisedTransactionsLog.aspx.designer.cs">
      <DependentUpon>customisedTransactionsLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dollarStatementGen.aspx.cs">
      <DependentUpon>dollarStatementGen.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dollarStatementGen.aspx.designer.cs">
      <DependentUpon>dollarStatementGen.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dollarStatementNavPrices.aspx.cs">
      <DependentUpon>dollarStatementNavPrices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dollarStatementNavPrices.aspx.designer.cs">
      <DependentUpon>dollarStatementNavPrices.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dollarStatements.aspx.cs">
      <DependentUpon>dollarStatements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dollarStatements.aspx.designer.cs">
      <DependentUpon>dollarStatements.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dollarStatementTransactions.aspx.cs">
      <DependentUpon>dollarStatementTransactions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dollarStatementTransactions.aspx.designer.cs">
      <DependentUpon>dollarStatementTransactions.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dollarStatementTransactionsNew.aspx.cs">
      <DependentUpon>dollarStatementTransactionsNew.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dollarStatementTransactionsNew.aspx.designer.cs">
      <DependentUpon>dollarStatementTransactionsNew.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/edit-account-details.aspx.cs">
      <DependentUpon>edit-account-details.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/edit-account-details.aspx.designer.cs">
      <DependentUpon>edit-account-details.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/editAccount.aspx.cs">
      <DependentUpon>editAccount.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/editAccount.aspx.designer.cs">
      <DependentUpon>editAccount.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/expenseCalculator.aspx.cs">
      <DependentUpon>expenseCalculator.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/expenseCalculator.aspx.designer.cs">
      <DependentUpon>expenseCalculator.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/feesncommission.aspx.cs">
      <DependentUpon>feesncommission.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/feesncommission.aspx.designer.cs">
      <DependentUpon>feesncommission.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/giftcards.aspx.cs">
      <DependentUpon>giftcards.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/giftcards.aspx.designer.cs">
      <DependentUpon>giftcards.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/HolidayManager.aspx.cs">
      <DependentUpon>HolidayManager.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/HolidayManager.aspx.designer.cs">
      <DependentUpon>HolidayManager.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/inflowlog.aspx.cs">
      <DependentUpon>inflowlog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/inflowlog.aspx.designer.cs">
      <DependentUpon>inflowlog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/InstructionLetter.aspx.cs">
      <DependentUpon>InstructionLetter.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/InstructionLetter.aspx.designer.cs">
      <DependentUpon>InstructionLetter.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/InvestmentLetterApprovals.aspx.cs">
      <DependentUpon>InvestmentLetterApprovals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/InvestmentLetterApprovals.aspx.designer.cs">
      <DependentUpon>InvestmentLetterApprovals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/InvestmentLetterGen.aspx.cs">
      <DependentUpon>InvestmentLetterGen.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/InvestmentLetterGen.aspx.designer.cs">
      <DependentUpon>InvestmentLetterGen.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/InvestmentOpportunitiesListing.aspx.cs">
      <DependentUpon>InvestmentOpportunitiesListing.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/InvestmentOpportunitiesListing.aspx.designer.cs">
      <DependentUpon>InvestmentOpportunitiesListing.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/LiquidationRequests.aspx.cs">
      <DependentUpon>LiquidationRequests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/LiquidationRequests.aspx.designer.cs">
      <DependentUpon>LiquidationRequests.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/newClientJoint.aspx.cs">
      <DependentUpon>newClientJoint.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/newClientJoint.aspx.designer.cs">
      <DependentUpon>newClientJoint.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/newComplaintLog.aspx.cs">
      <DependentUpon>newComplaintLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/newComplaintLog.aspx.designer.cs">
      <DependentUpon>newComplaintLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/portalupdatedetails.aspx.cs">
      <DependentUpon>portalupdatedetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/portalupdatedetails.aspx.designer.cs">
      <DependentUpon>portalupdatedetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/portalUpdateRequests.aspx.cs">
      <DependentUpon>portalUpdateRequests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/portalUpdateRequests.aspx.designer.cs">
      <DependentUpon>portalUpdateRequests.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/PepClientsDetails.aspx.cs">
      <DependentUpon>PepClientsDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/PepClientsDetails.aspx.designer.cs">
      <DependentUpon>PepClientsDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/PepRegistration.aspx.cs">
      <DependentUpon>PepRegistration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/PepRegistration.aspx.designer.cs">
      <DependentUpon>PepRegistration.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/OldClientBvnValidation.aspx.cs">
      <DependentUpon>OldClientBvnValidation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/OldClientBvnValidation.aspx.designer.cs">
      <DependentUpon>OldClientBvnValidation.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/premiumFinanceLog.aspx.cs">
      <DependentUpon>premiumFinanceLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/premiumFinanceLog.aspx.designer.cs">
      <DependentUpon>premiumFinanceLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/prospectDetailsApprovals.aspx.cs">
      <DependentUpon>prospectDetailsApprovals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/prospectDetailsApprovals.aspx.designer.cs">
      <DependentUpon>prospectDetailsApprovals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/queries\query-resource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>query-resource.resx</DependentUpon>
    </Compile>
    <Compile Include="**/riskProfilerSelections.aspx.cs">
      <DependentUpon>riskProfilerSelections.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/riskProfilerSelections.aspx.designer.cs">
      <DependentUpon>riskProfilerSelections.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/RMBTransactionLogs.aspx.cs">
      <DependentUpon>RMBTransactionLogs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/RMBTransactionLogs.aspx.designer.cs">
      <DependentUpon>RMBTransactionLogs.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/PrescheduledLiquidations.aspx.cs">
      <DependentUpon>PrescheduledLiquidations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/PrescheduledLiquidations.aspx.designer.cs">
      <DependentUpon>PrescheduledLiquidations.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/offlinePrescheduledRequest.aspx.cs">
      <DependentUpon>offlinePrescheduledRequest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/offlinePrescheduledRequest.aspx.designer.cs">
      <DependentUpon>offlinePrescheduledRequest.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/products.aspx.cs">
      <DependentUpon>products.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/products.aspx.designer.cs">
      <DependentUpon>products.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/pssCommission.aspx.cs">
      <DependentUpon>pssCommission.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/pssCommission.aspx.designer.cs">
      <DependentUpon>pssCommission.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/pssusers.aspx.cs">
      <DependentUpon>pssusers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/pssusers.aspx.designer.cs">
      <DependentUpon>pssusers.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/purchases.aspx.cs">
      <DependentUpon>purchases.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/purchases.aspx.designer.cs">
      <DependentUpon>purchases.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/RecurringDebits.aspx.cs">
      <DependentUpon>RecurringDebits.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/RecurringDebits.aspx.designer.cs">
      <DependentUpon>RecurringDebits.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/referralPoints.aspx.cs">
      <DependentUpon>referralPoints.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/referralPoints.aspx.designer.cs">
      <DependentUpon>referralPoints.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/RiskProfiler.aspx.cs">
      <DependentUpon>RiskProfiler.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/RiskProfiler.aspx.designer.cs">
      <DependentUpon>RiskProfiler.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/adminusersApprovals.aspx.cs">
      <DependentUpon>adminusersApprovals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/adminusersApprovals.aspx.designer.cs">
      <DependentUpon>adminusersApprovals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/approvals.aspx.cs">
      <DependentUpon>approvals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/approvals.aspx.designer.cs">
      <DependentUpon>approvals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumCredencePostHistory.aspx.cs">
      <DependentUpon>aumCredencePostHistory.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumCredencePostHistory.aspx.designer.cs">
      <DependentUpon>aumCredencePostHistory.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumEntries.aspx.cs">
      <DependentUpon>aumEntries.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumEntries.aspx.designer.cs">
      <DependentUpon>aumEntries.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumInterestProceeds.aspx.cs">
      <DependentUpon>aumInterestProceeds.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumInterestProceeds.aspx.designer.cs">
      <DependentUpon>aumInterestProceeds.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumTransactionsApprovals.aspx.cs">
      <DependentUpon>aumTransactionsApprovals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumTransactionsApprovals.aspx.designer.cs">
      <DependentUpon>aumTransactionsApprovals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/hniCustomers.aspx.cs">
      <DependentUpon>hniCustomers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/hniCustomers.aspx.designer.cs">
      <DependentUpon>hniCustomers.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/aumInvestmentPeriods.aspx.cs">
      <DependentUpon>aumInvestmentPeriods.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumInvestmentPeriods.aspx.designer.cs">
      <DependentUpon>aumInvestmentPeriods.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/IssuerConfiguration.aspx.cs">
      <DependentUpon>IssuerConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/IssuerConfiguration.aspx.designer.cs">
      <DependentUpon>IssuerConfiguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/manageCounterParty.aspx.cs">
      <DependentUpon>manageCounterParty.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/manageCounterParty.aspx.designer.cs">
      <DependentUpon>manageCounterParty.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/fdRates.aspx.cs">
      <DependentUpon>fdRates.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/fdRates.aspx.designer.cs">
      <DependentUpon>fdRates.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/App_Start\RouteConfig.cs" />
    <Compile Include="**/authorizeFDOrders.aspx.cs">
      <DependentUpon>authorizeFDOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeFDOrders.aspx.designer.cs">
      <DependentUpon>authorizeFDOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/ClientTagging.aspx.cs">
      <DependentUpon>ClientTagging.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/ClientTagging.aspx.designer.cs">
      <DependentUpon>ClientTagging.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\CredenceAPI\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\CredenceWS\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\EkomiWebService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/adminusers.aspx.cs">
      <DependentUpon>adminusers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/adminusers.aspx.designer.cs">
      <DependentUpon>adminusers.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Connected Services\KycService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="**/JumiaSummary.aspx.cs">
      <DependentUpon>JumiaSummary.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/JumiaSummary.aspx.designer.cs">
      <DependentUpon>JumiaSummary.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/JumiaCommissions.aspx.cs">
      <DependentUpon>JumiaCommissions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/JumiaCommissions.aspx.designer.cs">
      <DependentUpon>JumiaCommissions.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/manageFdOrders.aspx.cs">
      <DependentUpon>manageFdOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/manageFdOrders.aspx.designer.cs">
      <DependentUpon>manageFdOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/manageBills.aspx.cs">
      <DependentUpon>manageBills.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/manageBills.aspx.designer.cs">
      <DependentUpon>manageBills.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/manageBonds.aspx.cs">
      <DependentUpon>manageBonds.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/manageBonds.aspx.designer.cs">
      <DependentUpon>manageBonds.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/authorizeBonds.aspx.cs">
      <DependentUpon>authorizeBonds.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeBonds.aspx.designer.cs">
      <DependentUpon>authorizeBonds.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/authorizeNewClients.aspx.cs">
      <DependentUpon>authorizeNewClients.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeNewClients.aspx.designer.cs">
      <DependentUpon>authorizeNewClients.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/authorizeFDdeals.aspx.cs">
      <DependentUpon>authorizeFDdeals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeFDdeals.aspx.designer.cs">
      <DependentUpon>authorizeFDdeals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/authorizeEquities.aspx.cs">
      <DependentUpon>authorizeEquities.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeEquities.aspx.designer.cs">
      <DependentUpon>authorizeEquities.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/DashBoard.aspx.cs">
      <DependentUpon>DashBoard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/DashBoard.aspx.designer.cs">
      <DependentUpon>DashBoard.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/**/Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="**/Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/embassyletter.aspx.cs">
      <DependentUpon>embassyletter.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/embassyletter.aspx.designer.cs">
      <DependentUpon>embassyletter.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/manageFdSettlement.aspx.cs">
      <DependentUpon>manageFdSettlement.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/manageFdSettlement.aspx.designer.cs">
      <DependentUpon>manageFdSettlement.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/NewRequests.aspx.cs">
      <DependentUpon>NewRequests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/NewRequests.aspx.designer.cs">
      <DependentUpon>NewRequests.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/PaymentLogs.aspx.cs">
      <DependentUpon>PaymentLogs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/PaymentLogs.aspx.designer.cs">
      <DependentUpon>PaymentLogs.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/pssinvestomania.aspx.cs">
      <DependentUpon>pssinvestomania.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/pssinvestomania.aspx.designer.cs">
      <DependentUpon>pssinvestomania.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/investomania.aspx.cs">
      <DependentUpon>investomania.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/investomania.aspx.designer.cs">
      <DependentUpon>investomania.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/RoboAdvisor.aspx.cs">
      <DependentUpon>RoboAdvisor.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/RoboAdvisor.aspx.designer.cs">
      <DependentUpon>RoboAdvisor.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/securities.aspx.cs">
      <DependentUpon>securities.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/securities.aspx.designer.cs">
      <DependentUpon>securities.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/serviceLogs.aspx.cs">
      <DependentUpon>serviceLogs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/serviceLogs.aspx.designer.cs">
      <DependentUpon>serviceLogs.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/smartConsole.aspx.cs">
      <DependentUpon>smartConsole.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/smartConsole.aspx.designer.cs">
      <DependentUpon>smartConsole.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="**/kycVerificationStats.aspx.cs">
      <DependentUpon>kycVerificationStats.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/kycVerificationStats.aspx.designer.cs">
      <DependentUpon>kycVerificationStats.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="**/aumposition.aspx.cs">
      <DependentUpon>aumposition.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/aumposition.aspx.designer.cs">
      <DependentUpon>aumposition.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/smartStatements.aspx.cs">
      <DependentUpon>smartStatements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/smartStatements.aspx.designer.cs">
      <DependentUpon>smartStatements.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/smartRecon.aspx.cs">
      <DependentUpon>smartRecon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/smartRecon.aspx.designer.cs">
      <DependentUpon>smartRecon.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/stpentries.aspx.cs">
      <DependentUpon>stpentries.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/stpentries.aspx.designer.cs">
      <DependentUpon>stpentries.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/STRequests.aspx.cs">
      <DependentUpon>STRequests.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/STRequests.aspx.designer.cs">
      <DependentUpon>STRequests.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/ticketOrderCreate.aspx.cs">
      <DependentUpon>ticketOrderCreate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/ticketOrderCreate.aspx.designer.cs">
      <DependentUpon>ticketOrderCreate.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/ticketOrdersApproval.aspx.cs">
      <DependentUpon>ticketOrdersApproval.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/ticketOrdersApproval.aspx.designer.cs">
      <DependentUpon>ticketOrdersApproval.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/untaggedClients.aspx.cs">
      <DependentUpon>untaggedClients.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/untaggedClients.aspx.designer.cs">
      <DependentUpon>untaggedClients.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/untaggedClientsForApproval.aspx.cs">
      <DependentUpon>untaggedClientsForApproval.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/untaggedClientsForApproval.aspx.designer.cs">
      <DependentUpon>untaggedClientsForApproval.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/untaggedPssClients.aspx.cs">
      <DependentUpon>untaggedPssClients.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/untaggedPssClients.aspx.designer.cs">
      <DependentUpon>untaggedPssClients.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/untaggedPssClientsApproval.aspx.cs">
      <DependentUpon>untaggedPssClientsApproval.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/untaggedPssClientsApproval.aspx.designer.cs">
      <DependentUpon>untaggedPssClientsApproval.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/updateBankDetails.aspx.cs">
      <DependentUpon>updateBankDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/updateBankDetails.aspx.designer.cs">
      <DependentUpon>updateBankDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/newClient.aspx.cs">
      <DependentUpon>newClient.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/newClient.aspx.designer.cs">
      <DependentUpon>newClient.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/Properties\AssemblyInfo.cs" />
    <Compile Include="**/Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="**/authorizeCPdeals.aspx.cs">
      <DependentUpon>authorizeCPdeals.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/authorizeCPdeals.aspx.designer.cs">
      <DependentUpon>authorizeCPdeals.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/dealListing.aspx.cs">
      <DependentUpon>dealListing.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/dealListing.aspx.designer.cs">
      <DependentUpon>dealListing.aspx</DependentUpon>
    </Compile>
    <Compile Include="smartInflowLog.aspx.cs">
      <DependentUpon>smartInflowLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/smartInflowLog.aspx.designer.cs">
      <DependentUpon>smartInflowLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/addBankDetails.aspx.cs">
      <DependentUpon>addBankDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/addBankDetails.aspx.designer.cs">
      <DependentUpon>addBankDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/PepListings.aspx.cs">
      <DependentUpon>PepListings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/PepListings.aspx.designer.cs">
      <DependentUpon>PepListings.aspx</DependentUpon>
    </Compile>
    <Compile Include="uploadInflowOutflow.aspx.cs">
      <DependentUpon>uploadInflowOutflow.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/uploadInflowOutflow.aspx.designer.cs">
      <DependentUpon>uploadInflowOutflow.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/newOfflineRequest.aspx.cs">
      <DependentUpon>newOfflineRequest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/newOfflineRequest.aspx.designer.cs">
      <DependentUpon>newOfflineRequest.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/uploadOutflowLog.aspx.cs">
      <DependentUpon>uploadOutflowLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/uploadOutflowLog.aspx.designer.cs">
      <DependentUpon>uploadOutflowLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/uploadStatements.aspx.cs">
      <DependentUpon>uploadStatements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/uploadStatements.aspx.designer.cs">
      <DependentUpon>uploadStatements.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/userdetails.aspx.cs">
      <DependentUpon>userdetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/userdetails.aspx.designer.cs">
      <DependentUpon>userdetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/verifyKYC.aspx.cs">
      <DependentUpon>verifyKYC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/verifyKYC.aspx.designer.cs">
      <DependentUpon>verifyKYC.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
    <Compile Include="**/fundPerformances.aspx.cs">
      <DependentUpon>fundPerformances.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/fundPerformances.aspx.designer.cs">
      <DependentUpon>fundPerformances.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/errorPage.aspx.cs">
      <DependentUpon>errorPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/errorPage.aspx.designer.cs">
      <DependentUpon>errorPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="**/pssAgentsMakerChecker.aspx.cs">
      <DependentUpon>pssAgentsMakerChecker.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="**/pssAgentsMakerChecker.aspx.designer.cs">
      <DependentUpon>pssAgentsMakerChecker.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="handlers\" />
    <Folder Include="templates\pdf\downloads\" />
    <Folder Include="templates\word\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="**/css\bootstrap-datepicker3.min.css.map" />
    <Content Include="**/font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="**/font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="**/font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="**/font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="**/font-awesome\fonts\FontAwesome.otf" />
    <Content Include="**/mine\js\amcharts_3.20.8.free.zip" />
    <Content Include="**/fonts\glyphicons-halflings-regular.eot" />
    <Content Include="**/fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="**/fonts\glyphicons-halflings-regular.woff" />
    <Content Include="**/fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="**/css\plugins\footable\fonts\footable.eot" />
    <Content Include="**/css\plugins\footable\fonts\footable.ttf" />
    <Content Include="**/css\plugins\footable\fonts\footable.woff" />
    <Content Include="**/css\plugins\footable\fonts\footabled41d.eot" />
    <Content Include="**/css\plugins\slick\fonts\slick.eot" />
    <Content Include="**/css\plugins\slick\fonts\slick.ttf" />
    <Content Include="**/css\plugins\slick\fonts\slick.woff" />
    <Content Include="**/css\plugins\slick\fonts\slickd41d.eot" />
    <Content Include="**/css\plugins\summernote\font\summernote9d3d.eot" />
    <Content Include="**/css\plugins\summernote\font\summernote9d3d.ttf" />
    <Content Include="**/css\plugins\summernote\font\summernote9d3d.woff" />
    <Content Include="**/css\plugins\summernote\font\summernoted41d.eot" />
    <None Include="Connected Services\CredenceWS\iwebzWebServices.wsdl" />
    <Content Include="**/Connected Services\CredenceWS\WealthMart.Web.CredenceWS.clientResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceWS\WealthMart.Web.CredenceWS.mastersResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceWS\WealthMart.Web.CredenceWS.reportsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceWS\WealthMart.Web.CredenceWS.settingsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Connected Services\CredenceWS\WealthMart.Web.CredenceWS.transactionResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Content>
    <Content Include="**/Properties\DataSources\WealthMart.Framework.AxaBVNApi.Result.datasource" />
    <Content Include="**/Properties\DataSources\WealthMart.Framework.AxaBVNApi.TransactionResponseModel.datasource" />
    <Content Include="**/templates\BondNotificationDataTemplate.xlsx" />
    <Content Include="**/Site.Mobile.Master" />
    <Content Include="**/Scripts\umd\popper.min.js.map" />
    <Content Include="**/Scripts\umd\popper.js.map" />
    <Content Include="**/Scripts\umd\popper.js.flow" />
    <Content Include="**/Scripts\umd\popper-utils.min.js.map" />
    <Content Include="**/Scripts\umd\popper-utils.js.map" />
    <Content Include="**/Scripts\README.md" />
    <Content Include="**/Scripts\popper.min.js.map" />
    <Content Include="**/Scripts\popper.js.map" />
    <Content Include="**/Scripts\popper-utils.min.js.map" />
    <Content Include="**/Scripts\popper-utils.js.map" />
    <Content Include="**/Scripts\index.js.flow" />
    <Content Include="**/Scripts\esm\popper.min.js.map" />
    <Content Include="**/Scripts\esm\popper.js.map" />
    <Content Include="**/Scripts\esm\popper-utils.min.js.map" />
    <Content Include="**/Scripts\esm\popper-utils.js.map" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Connected Services\ADService\" />
    <WCFMetadataStorage Include="Connected Services\AxaBVNApi\" />
    <WCFMetadataStorage Include="Connected Services\CredenceAPI\" />
    <WCFMetadataStorage Include="Connected Services\CredenceWS\" />
    <WCFMetadataStorage Include="Connected Services\EkomiWebService\" />
    <WCFMetadataStorage Include="Connected Services\KycService\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="**/Scripts\index.d.ts" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AngleSharp">
      <Version>0.16.0</Version>
    </PackageReference>
    <PackageReference Include="Investment-Portal-Deps">
      <Version>1.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.FriendlyUrls">
      <Version>1.0.2</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Net.Compilers">
      <Version>2.9.0</Version>
    </PackageReference>
    <PackageReference Include="BCrypt.Net">
      <Version>0.1.0</Version>
    </PackageReference>
    <PackageReference Include="DocumentFormat.OpenXml">
      <Version>2.9.1</Version>
    </PackageReference>
    <!-- <PackageReference Include="ICSharpCode.SharpZipLib.dll"> -->
    <!-- <Version>0.85.4.369</Version> -->
    <!-- </PackageReference> -->
    <PackageReference Include="Ionic.Zip">
      <Version>*******</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.1</Version>
    </PackageReference>
    <PackageReference Include="NPOI">
      <Version>2.6.2</Version>
    </PackageReference>
    <PackageReference Include="Select.HtmlToPdf">
      <Version>22.1.0</Version>
    </PackageReference>
    <PackageReference Include="Z.EntityFramework.Extensions">
      <Version>*********</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Mvc">
      <Version>5.2.7</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Razor">
      <Version>3.2.7</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebPages">
      <Version>3.2.7</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions">
      <Version>2.2.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Web.Infrastructure">
      <Version>1.0.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="queries\query-resource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>query-resource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\WealthMart.Framework\WealthMart.Framework.csproj">
      <Project>{39565a9b-a009-4c4f-b72e-94a8695fe784}</Project>
      <Name>WealthMart.Framework</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <LangVersion>10.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <Optimize>true</Optimize>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <LangVersion>10.0</LangVersion>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>49790</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:59009/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
    <!-- <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.2.9.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.2.9.0\build\Microsoft.Net.Compilers.props'))" /> -->
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="BeforeBuild">
    <TransformXml Source="Web.Base.config" Transform="Web.$(Configuration).config" Destination="Web.config" />
  </Target>
</Project>